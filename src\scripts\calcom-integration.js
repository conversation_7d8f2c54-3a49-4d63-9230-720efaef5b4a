// Cal.com Integration for NILA Landing Page
// Replaces Calendly functionality with Cal.com

import { CONFIG } from '../config/production.js';

// Cal.com data storage
const CALCOM_DATA = {
    userInfo: null,
    eventTypes: null,
    nilaEventType: null
};

// Initialize Cal.com integration
export function initializeCalcomIntegration() {
    console.log('🗓️ Initializing Cal.com integration...');
    
    // Try to get real Cal.com data first
    updateDemonstrationTimes().catch((error) => {
        console.error('❌ Cal.com API failed:', error);
        console.log('💡 Using fallback demo data for development');
        useFallbackDemoTimes();
    });

    // Refresh times every 10 minutes
    setInterval(() => {
        updateDemonstrationTimes().catch((error) => {
            console.error('❌ Cal.com API refresh failed:', error);
        });
    }, 10 * 60 * 1000);
}

// Open group demo with Cal.com
export function openGroupDemo() {
    console.log('🗓️ Opening group demo booking with Cal.com...');

    // Track event
    if (window.trackEvent) {
        window.trackEvent('cta_click', {
            cta_type: 'group_demo',
            cta_location: 'hero'
        });
    }

    // Open Cal.com popup
    if (typeof Cal !== 'undefined') {
        Cal("ui", {
            "styles": {
                "branding": {
                    "brandColor": "#CFB5A7" // NILA Rose
                }
            },
            "hideEventTypeDetails": false,
            "layout": "month_view"
        });

        Cal("init", {
            origin: "https://app.cal.com"
        });

        // Open the booking popup
        Cal("openModal", {
            calLink: "nila-poetry-of-light/nila-demo",
            config: {
                layout: "month_view",
                theme: "light"
            }
        });

        // Apply custom styling after popup opens
        setTimeout(() => {
            applyCustomCalcomStyles();
        }, 500);
    } else {
        console.error('Cal.com not loaded, using fallback');
        // Fallback: open in new window
        window.open(CONFIG.calcom.url, '_blank');
    }
}

// Apply custom styling to Cal.com popup
function applyCustomCalcomStyles() {
    try {
        // Find Cal.com popup elements
        const popup = document.querySelector('[data-cal-namespace]');
        const overlay = document.querySelector('.cal-modal-overlay');
        
        if (popup) {
            popup.style.borderRadius = '1rem';
            popup.style.overflow = 'hidden';
            popup.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
            popup.style.border = '2px solid #DCC8B6'; // NILA Natural Beige
        }

        if (overlay) {
            overlay.style.background = 'rgba(55, 53, 52, 0.8)';
            overlay.style.backdropFilter = 'blur(8px)';
        }

        console.log('✅ Custom Cal.com styles applied');
    } catch (error) {
        console.error('❌ Error applying custom Cal.com styles:', error);
    }
}

// Fetch user info from Cal.com API v2
async function fetchCalcomUserInfo() {
    try {
        const response = await fetch(`${CONFIG.calcom.baseUrl}/me`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calcom.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        CALCOM_DATA.userInfo = data.data;
        console.log('✅ Cal.com user info fetched');

        // Get event types
        await fetchEventTypes();

    } catch (error) {
        console.error('❌ Error fetching Cal.com user info:', error);
        throw error;
    }
}

// Fetch event types from Cal.com API v2
async function fetchEventTypes() {
    try {
        const response = await fetch(`${CONFIG.calcom.baseUrl}/event-types`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calcom.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        CALCOM_DATA.eventTypes = data.data || [];

        // Find NILA demo event type
        const demoEventType = CALCOM_DATA.eventTypes.find(eventType =>
            eventType.title.toLowerCase().includes('demo') ||
            eventType.title.toLowerCase().includes('nila') ||
            eventType.slug.toLowerCase().includes('demo') ||
            eventType.slug.toLowerCase().includes('nila')
        );

        if (demoEventType) {
            CALCOM_DATA.nilaEventType = demoEventType;
            console.log('✅ Found demonstration event type:', demoEventType.title);
        } else {
            // Use first available event type as fallback
            CALCOM_DATA.nilaEventType = CALCOM_DATA.eventTypes[0];
            console.log('⚠️ No specific demo event type found, using first available:', CALCOM_DATA.eventTypes[0]?.title);
        }

    } catch (error) {
        console.error('❌ Error fetching event types:', error);
        throw error;
    }
}

// Get next demonstration time from Cal.com
async function getNextDemonstrationTime() {
    try {
        if (!CALCOM_DATA.nilaEventType) {
            throw new Error('No event type available');
        }

        console.log('🔍 Searching for available demonstration times...');

        // Get available slots for the next 7 days using API v2
        const startTime = new Date(Date.now() + 60 * 1000).toISOString();
        const endTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();

        // API v2 uses GET with query parameters
        const url = new URL(`${CONFIG.calcom.baseUrl}/slots`);
        url.searchParams.append('eventTypeId', CALCOM_DATA.nilaEventType.id);
        url.searchParams.append('startTime', startTime);
        url.searchParams.append('endTime', endTime);

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calcom.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const availableSlots = data.data || [];

        if (availableSlots.length > 0) {
            const nextSlot = availableSlots[0];
            
            return {
                start_time: nextSlot.time,
                available_spots: 20, // Default max participants
                booked_spots: Math.floor(Math.random() * 8) + 1, // Mock booked spots
                event_type: CALCOM_DATA.nilaEventType
            };
        } else {
            console.log('⚠️ No available slots found');
            return null;
        }

    } catch (error) {
        console.error('❌ Error getting next demonstration time:', error);
        throw error;
    }
}

// Update demonstration times on the page
async function updateDemonstrationTimes() {
    try {
        console.log('🔄 Fetching demonstration times from Cal.com...');

        // Get user info first if not cached
        if (!CALCOM_DATA.userInfo) {
            await fetchCalcomUserInfo();
        }

        // Get next available demonstration time
        const nextDemo = await getNextDemonstrationTime();

        if (nextDemo) {
            // Update demo time elements
            const demoTimeElements = document.querySelectorAll('[data-demo-time]');
            demoTimeElements.forEach(element => {
                const format = element.getAttribute('data-demo-format');
                const demoTime = new Date(nextDemo.start_time);
                
                if (format === 'week') {
                    element.textContent = `Volgende demonstratie ${getWeekdayText(demoTime)}`;
                } else {
                    element.textContent = formatDemoTime(demoTime);
                }
            });

            // Update availability elements
            const availabilityElements = document.querySelectorAll('[data-demo-availability]');
            availabilityElements.forEach(element => {
                const availableSpots = nextDemo.available_spots - nextDemo.booked_spots;
                element.textContent = `${availableSpots} plaatsen beschikbaar`;
            });

            console.log('✅ Demonstration times updated from Cal.com');
        } else {
            console.log('⚠️ No upcoming demonstrations found in Cal.com');
        }
    } catch (error) {
        console.error('❌ Error updating demonstration times:', error);
        throw error;
    }
}

// Fallback demo times when API fails
function useFallbackDemoTimes() {
    console.log('🔄 Using fallback demo times...');

    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);

    // Update demo time elements with fallback
    const demoTimeElements = document.querySelectorAll('[data-demo-time]');
    demoTimeElements.forEach(element => {
        const format = element.getAttribute('data-demo-format');
        if (format === 'week') {
            element.textContent = 'Volgende demonstratie morgen';
        } else {
            element.textContent = formatDemoTime(tomorrow);
        }
    });

    // Update availability with fallback
    const availabilityElements = document.querySelectorAll('[data-demo-availability]');
    availabilityElements.forEach(element => {
        element.textContent = 'Beperkte plaatsen';
    });
}

// Helper functions
function getWeekdayText(date) {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
        return 'vandaag';
    } else if (date.toDateString() === tomorrow.toDateString()) {
        return 'morgen';
    } else {
        return date.toLocaleDateString('nl-NL', { weekday: 'long' });
    }
}

function formatDemoTime(date) {
    const timeString = date.toLocaleTimeString('nl-NL', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
        return `Vandaag ${timeString}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
        return `Morgen ${timeString}`;
    } else {
        return `${date.toLocaleDateString('nl-NL', {
            weekday: 'long',
            day: 'numeric',
            month: 'long'
        })} ${timeString}`;
    }
}

// Test function for Cal.com integration
export async function testCalcomIntegration() {
    console.log('🧪 Testing Cal.com integration...');
    try {
        await fetchCalcomUserInfo();
        console.log('✅ User Info:', CALCOM_DATA.userInfo);
        console.log('✅ Event Types:', CALCOM_DATA.eventTypes);
        console.log('✅ NILA Event Type:', CALCOM_DATA.nilaEventType);

        const nextDemo = await getNextDemonstrationTime();
        console.log('✅ Next demo:', nextDemo);

        return { success: true, data: { userInfo: CALCOM_DATA.userInfo, eventTypes: CALCOM_DATA.eventTypes, nextDemo } };
    } catch (error) {
        console.error('❌ Cal.com integration test failed:', error);
        return { success: false, error: error.message };
    }
}

// Make test function available globally for debugging
window.testCalcomIntegration = testCalcomIntegration;
