@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts Import - NILA Brand Typography */
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500;600;700&family=DM+Sans:wght@300;400;500;600;700&display=swap');

/* Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'DM Sans', sans-serif;
    color: #373534; /* Night Black */
    background-color: #F8F4EC; /* Alabaster White */
    line-height: 1.6;
    font-weight: 300; /* DM Sans Light for body text */
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'IBM Plex Sans', sans-serif;
    color: #373534; /* Night Black */
    font-weight: 400; /* IBM Plex Sans Regular for headers */
  }

  .subheading {
    font-family: 'DM Sans', sans-serif;
    font-weight: 600; /* DM Sans SemiBold for subheaders */
  }

  /* NILA Brand Color Utilities */
  .text-nila-night-black { color: #373534; }
  .text-nila-natural-beige { color: #DCC8B6; }
  .text-nila-rose { color: #CFB5A7; }
  .text-nila-alabaster-white { color: #F8F4EC; }

  .bg-nila-night-black { background-color: #373534; }
  .bg-nila-natural-beige { background-color: #DCC8B6; }
  .bg-nila-rose { background-color: #CFB5A7; }
  .bg-nila-alabaster-white { background-color: #F8F4EC; }

  .border-nila-night-black { border-color: #373534; }
  .border-nila-natural-beige { border-color: #DCC8B6; }
  .border-nila-rose { border-color: #CFB5A7; }
  .border-nila-alabaster-white { border-color: #F8F4EC; }

  /* Legacy color mappings for gradual transition */
  .text-nila-navy { color: #373534; }
  .text-nila-gold { color: #DCC8B6; }
  .text-nila-accent-gold { color: #CFB5A7; }
  .text-nila-dark-gray { color: #373534; }
  .text-nila-warm-white { color: #F8F4EC; }
  .text-nila-light-gray { color: #F8F4EC; }

  .bg-nila-navy { background-color: #373534; }
  .bg-nila-gold { background-color: #DCC8B6; }
  .bg-nila-accent-gold { background-color: #CFB5A7; }
  .bg-nila-dark-gray { background-color: #373534; }
  .bg-nila-warm-white { background-color: #F8F4EC; }
  .bg-nila-light-gray { background-color: #F8F4EC; }

  .border-nila-navy { border-color: #373534; }
  .border-nila-gold { border-color: #DCC8B6; }
  .border-nila-accent-gold { border-color: #CFB5A7; }
  .border-nila-dark-gray { border-color: #373534; }
  .border-nila-warm-white { border-color: #F8F4EC; }
  .border-nila-light-gray { border-color: #F8F4EC; }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: #F8F4EC; /* Alabaster White */
  }

  ::-webkit-scrollbar-thumb {
    background-color: #DCC8B6; /* Natural Beige */
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #CFB5A7; /* Rose */
  }
}

/* Component Styles */
@layer components {
  /* CTA Buttons */
  .btn-primary {
    background-color: #DCC8B6; /* Natural Beige */
    color: #373534; /* Night Black */
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(0);
    transition: all 0.3s ease-out;
  }

  .btn-primary:hover {
    background-color: #CFB5A7; /* Rose */
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  .btn-secondary {
    background-color: transparent;
    border: 2px solid #DCC8B6; /* Natural Beige */
    color: #DCC8B6; /* Natural Beige */
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease-out;
  }

  .btn-secondary:hover {
    background-color: #DCC8B6; /* Natural Beige */
    color: #373534; /* Night Black */
  }

  /* Enhanced Hero CTA Buttons */
  .btn-primary-hero {
    /* Layout */
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    /* Fixed dimensions - exactly 320px x 80px */
    width: 320px;
    height: 80px;
    min-width: 320px;
    min-height: 80px;
    max-width: 320px;
    max-height: 80px;

    /* Styling */
    background: linear-gradient(135deg, #DCC8B6 0%, #CFB5A7 100%);
    color: #373534;
    font-weight: 700;
    padding: 0.75rem 1.5rem;
    border-radius: 16px;
    border: 4px solid rgba(248, 244, 236, 0.6);
    box-shadow:
      0 15px 30px rgba(220, 200, 182, 0.6),
      0 8px 16px rgba(55, 53, 52, 0.2),
      inset 0 2px 0 rgba(248, 244, 236, 0.4);

    /* Animation */
    transform: translateY(0);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;

    /* Typography */
    font-size: 1rem;
    text-decoration: none;

    /* Interaction */
    z-index: 20;
    cursor: pointer;
  }

  .btn-primary-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(248, 244, 236, 0.5),
      transparent
    );
    transition: left 0.8s ease;
  }

  .btn-primary-hero:hover::before {
    left: 100%;
  }

  .btn-primary-hero:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow:
      0 20px 40px rgba(220, 200, 182, 0.7),
      0 12px 24px rgba(55, 53, 52, 0.3),
      inset 0 2px 0 rgba(248, 244, 236, 0.5);
    border-color: rgba(248, 244, 236, 0.7);
    background: linear-gradient(135deg, #CFB5A7 0%, #DCC8B6 100%);
  }

  .btn-primary-hero:active {
    transform: translateY(-3px) scale(1.05);
  }

  .btn-secondary-hero {
    /* Layout */
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    /* Fixed dimensions - exactly 320px x 80px */
    width: 320px;
    height: 80px;
    min-width: 320px;
    min-height: 80px;
    max-width: 320px;
    max-height: 80px;

    /* Styling */
    background: rgba(248, 244, 236, 0.15);
    border: 4px solid rgba(248, 244, 236, 0.6);
    color: #F8F4EC;
    font-weight: 700;
    padding: 0.75rem 1.5rem;
    border-radius: 16px;
    backdrop-filter: blur(20px);
    box-shadow:
      0 10px 25px rgba(248, 244, 236, 0.2),
      inset 0 2px 0 rgba(248, 244, 236, 0.3);

    /* Animation */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* Typography */
    font-size: 1rem;
    text-decoration: none;

    /* Interaction */
    cursor: pointer;
  }

  .btn-secondary-hero:hover {
    background: rgba(248, 244, 236, 0.25);
    border-color: rgba(248, 244, 236, 0.7);
    color: #F8F4EC;
    transform: translateY(-3px) scale(1.03);
    box-shadow:
      0 15px 35px rgba(248, 244, 236, 0.3),
      inset 0 2px 0 rgba(248, 244, 236, 0.4);
  }

  /* Accessibility Improvements */
  .btn-primary-hero:focus,
  .btn-secondary-hero:focus {
    outline: 3px solid #CFB5A7;
    outline-offset: 2px;
  }

  /* Loading States */
  .btn-loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
  }

  .btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Accessibility Improvements */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #373534;
    color: #F8F4EC;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Screen reader only class */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus\:not-sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem 1rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* Section Containers */
  .section-container {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .section-container {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .section-container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  .section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-top: 6rem;
      padding-bottom: 6rem;
    }
  }

  /* Hero Section */
  .hero-overlay {
    background: linear-gradient(
      135deg,
      rgba(55, 53, 52, 0.85) 0%,
      rgba(55, 53, 52, 0.75) 40%,
      rgba(55, 53, 52, 0.6) 70%,
      rgba(220, 200, 182, 0.3) 100%
    );
    animation: overlay-fade-in 2.5s ease-out forwards;
  }

  @keyframes overlay-fade-in {
    0% {
      background: linear-gradient(
        135deg,
        rgba(55, 53, 52, 0.9) 0%,
        rgba(55, 53, 52, 0.7) 50%,
        rgba(220, 200, 182, 0.4) 100%
      );
    }
    100% {
      background: linear-gradient(
        135deg,
        rgba(55, 53, 52, 0.7) 0%,
        rgba(55, 53, 52, 0.4) 50%,
        rgba(220, 200, 182, 0.2) 100%
      );
    }
  }

  /* Form Styles */
  .form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #DCC8B6; /* Natural Beige */
    border-radius: 0.5rem;
    background-color: #F8F4EC; /* Alabaster White */
    color: #373534; /* Night Black */
    transition: all 0.2s;
  }

  .form-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px #CFB5A7; /* Rose */
    border-color: transparent;
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #373534; /* Night Black */
    margin-bottom: 0.5rem;
  }

  /* Modal/Popup Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
  }

  .modal-overlay.hidden {
    display: none !important;
  }

  .modal-content {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    max-width: 28rem;
    width: 100%;
    max-height: 100vh;
    overflow-y: auto;
  }

  /* Loading States */
  .loading-spinner {
    animation: spin 1s linear infinite;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    border-bottom: 2px solid #DCC8B6; /* Natural Beige */
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* Image Styles */
  .w-full.h-96.object-cover.rounded-xl.shadow-2xl {
    transition: all 0.3s ease;
  }

  .w-full.h-96.object-cover.rounded-xl.shadow-2xl:hover {
    transform: scale(1.03);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* Meer naar links + focus op BOVENKANT waar lamp staat */
    object-position: 5% 25%;
    /* Uitzoomen voor betere compositie */
    transform: scale(1.6) translateX(-20%);
    animation: hero-image-entrance 3s ease-out forwards;
    transition: transform 0.3s ease-out;
  }

  @keyframes hero-image-entrance {
    0% {
      transform: scale(1.4) translateX(-15%) translateY(20px);
      opacity: 0.7;
      filter: blur(2px);
    }
    50% {
      opacity: 0.9;
      filter: blur(1px);
    }
    100% {
      transform: scale(1.6) translateX(-18%) translateY(0);
      opacity: 1;
      filter: blur(0);
    }
  }

  /* Subtle parallax effect on scroll */
  #hero:hover .hero-image {
    transform: scale(1.7) translateX(-20%);
  }

  /* For vertical images that need horizontal cropping */
  @media (max-aspect-ratio: 16/9) {
    .hero-image {
      /* Voor verticale images: lamp meer naar links + focus op BOVENKANT */
      object-position: 35% 25%;
      transform: scale(2) translateX(-25%);
    }

    @keyframes hero-image-entrance {
      0% {
        transform: scale(1.8) translateX(-18%);
        opacity: 0.8;
      }
      100% {
        transform: scale(2.0) translateX(-20%);
        opacity: 1;
      }
    }
  }

  .product-image {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.5s ease;
  }

  .product-image:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: scale(1.2);
    object-position: 25% 50%;
  }

  /* Craftsmanship image hover effect */
  .craftsmanship-image-container {
    overflow: hidden;
    border-radius: 0.75rem;
  }

  .craftsmanship-image-container img {
    transition: all 0.5s ease;
  }

  .craftsmanship-image-container:hover img {
    transform: scale(1.2);
    object-position: 25% 50%;
  }

  /* Nederlands vakmanschap image zoom effect */
  .nederlands-vakmanschap-image {
    transform: scale(1.0);
    object-position: 33% 50%; /* 1/3 van links, midden verticaal */
    transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), object-position 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, object-position;
  }

  /* Enhanced Visual Effects */
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .image-overlay-hover {
    transition: all 0.3s ease;
  }

  .image-overlay-hover:hover {
    transform: scale(1.02);
  }

  .image-overlay-hover:hover .overlay-content {
    opacity: 1;
    transform: translateY(0);
  }

  .overlay-content {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
  }
}

/* Utility Classes */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .backdrop-blur-light {
    backdrop-filter: blur(8px);
  }

  /* Animation utilities */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }

  .animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Sales-focused animations */
  .urgent-shake {
    animation: shake 0.5s ease-in-out;
  }

  .flash-attention {
    animation: flash 1s ease-in-out infinite;
  }

  .urgent-pulse {
    animation: urgentPulse 1.5s ease-in-out infinite;
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }

  @keyframes flash {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  @keyframes urgentPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
  }

  /* Sales-specific styles */
  .scarcity-badge {
    background-color: #ef4444; /* red-500 */
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 700;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .conversion-highlight {
    background-color: #fef08a; /* yellow-200 */
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
  }

  .social-proof-star {
    color: #fbbf24;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  .hero-overlay {
    background: linear-gradient(
      180deg,
      rgba(55, 53, 52, 0.8) 0%,
      rgba(55, 53, 52, 0.6) 70%,
      rgba(220, 200, 182, 0.3) 100%
    );
  }

  /* Mobile: Vervang img met background voor betere controle */
  .hero-image-container {
    position: relative;
  }

  .hero-image {
    /* Verberg originele image op mobile */
    display: none;
  }

  /* Mobile: Gebruik background-image voor extreme crop controle */
  .hero-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/src/assets/images/nila-flush-hero.jpg');
    background-size: 400% auto; /* EXTREME zoom om lamp te isoleren */
    background-position: 66% 75%; /* Focus precies op lamp */
    background-repeat: no-repeat;
    z-index: 1;
  }

  /* Mobile Typography Improvements */
  .text-hero {
    font-size: 2.5rem !important;
    line-height: 1.2 !important;
  }

  /* Mobile CTA Improvements */
  .hero-cta-buttons {
    flex-direction: column !important;
    gap: 1rem !important;
    margin-bottom: 2rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .hero-cta-buttons button {
    width: 320px !important;
    height: 80px !important;
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
    max-width: 320px !important;
    margin: 0 auto !important;
  }

  /* Mobile Hero Content Spacing */
  #hero {
    min-height: 100vh !important;
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  /* Mobile Section Spacing */
  .section-padding {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  /* Mobile Text Improvements - Consistent padding for all sections */
  .section-container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Ensure all content respects mobile margins */
  main section {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  /* Fix text alignment and spacing */
  .text-center {
    text-align: center !important;
  }

  /* Ensure grid items don't overflow */
  .grid > * {
    min-width: 0 !important;
  }

  /* Mobile Hero Section Spacing */
  #hero .section-container {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  /* Mobile Logo Spacing */
  #hero .mb-8 {
    margin-bottom: 2.5rem !important;
  }

  /* Mobile Live Activity Indicators - Fix bottom spacing */
  .live-activity-indicators {
    margin-bottom: 3rem !important;
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
    flex-wrap: wrap !important;
    gap: 1.5rem !important;
    justify-content: center !important;
  }

  /* Mobile Live Activity Individual Items */
  .live-activity-indicators > div {
    flex: 1 1 auto !important;
    min-width: 0 !important;
    text-align: center !important;
    font-size: 0.75rem !important;
  }

  /* Mobile Scroll Indicator - More space from bottom */
  #hero .absolute.bottom-8 {
    bottom: 2rem !important;
  }

  /* Mobile Gallery Grid */
  .gallery-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Mobile Form Improvements */
  .modal-content {
    margin: 0.5rem !important;
    max-height: 95vh !important;
    overflow-y: auto !important;
    max-width: calc(100vw - 1rem) !important;
    width: calc(100vw - 1rem) !important;
  }

  /* Mobile Modal Overlay */
  .modal-overlay {
    padding: 0.5rem !important;
  }

  /* Mobile Form Elements */
  .modal-content input,
  .modal-content select,
  .modal-content textarea {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }

  /* Mobile Form Buttons */
  .modal-content button {
    padding: 0.875rem 1.5rem !important;
    font-size: 1rem !important;
  }

  /* Mobile Sticky CTA - Less aggressive */
  #sticky-cta-bar {
    display: none !important; /* Hide sticky bar on mobile - too aggressive */
  }

  /* Mobile Floating CTA - Show instead */
  #floating-cta {
    display: block !important;
    bottom: 1rem !important;
    right: 1rem !important;
  }

  /* Mobile testimonial cards - better spacing */
  .grid.md\\:grid-cols-2 > div {
    margin-bottom: 1rem !important;
  }
}

/* Desktop: Show normal image */
@media (min-width: 769px) {
  .hero-image {
    display: block !important;
  }

  .hero-image-container::before {
    display: none !important;
  }
}

/* Tablet Responsive Design */
@media (min-width: 769px) and (max-width: 1024px) {
  .hero-image {
    /* Tablet: lamp meer naar links + focus op BOVENKANT */
    object-position: 12% 25%;
    transform: scale(1.7) translateX(-15%);
  }

  .text-hero {
    font-size: 3rem;
  }

  .section-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Large Screen Optimizations */
@media (min-width: 1440px) {
  .section-container {
    max-width: 1400px;
  }

  .hero-image {
    /* Large screens: lamp meer naar links + focus op BOVENKANT */
    object-position: 15% 25%;
    transform: scale(1.8) translateX(-12%);
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}
