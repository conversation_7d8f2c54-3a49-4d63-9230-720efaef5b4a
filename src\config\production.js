// Production Configuration for NILA Landing Page
// Update these values before deploying to production

export const PRODUCTION_CONFIG = {
    // Cal.com Integration
    calcom: {
        url: 'https://cal.com/nila/demo', // VERVANG MET ECHTE CAL.COM URL (bijv. https://cal.com/username/event-type)
        apiToken: 'cal_live_1dc5e2e1320cf007c1dff0e00e9d458a',
        baseUrl: 'https://api.cal.com/v1',
        username: 'nila', // VERVANG MET ECHTE CAL.COM USERNAME
        eventTypeSlug: 'demo', // VERVANG MET ECHTE EVENT TYPE SLUG
        eventTypeId: null, // Wordt automatisch opgehaald via API
        utm: {
            utmCampaign: 'nila_landing',
            utmSource: 'website',
            utmMedium: 'cta'
        }
    },
    
    // n8n Webhook Configuration
    webhook: {
        url: 'https://your-n8n-webhook-url.com/webhook/nila-samples', // VERVANG MET ECHTE WEBHOOK URL
        apiKey: 'your-api-key-here', // VERVANG MET ECHTE API KEY
        retryAttempts: 3,
        retryDelay: 1000 // milliseconds
    },
    
    // Analytics Configuration
    analytics: {
        googleAnalytics: {
            measurementId: 'GA_MEASUREMENT_ID', // VERVANG MET ECHTE GA4 ID
            enabled: true
        },
        facebookPixel: {
            pixelId: 'FB_PIXEL_ID', // VERVANG MET ECHTE FACEBOOK PIXEL ID
            enabled: true
        },
        hotjar: {
            siteId: 'HOTJAR_SITE_ID', // VERVANG MET ECHTE HOTJAR ID
            enabled: false // Zet op true als Hotjar gebruikt wordt
        }
    },
    
    // Performance Settings
    performance: {
        lazyLoadImages: true,
        enableServiceWorker: false, // Zet op true voor PWA functionaliteit
        compressionLevel: 'high'
    },
    
    // GDPR & Privacy
    privacy: {
        cookieConsentRequired: true,
        privacyPolicyUrl: 'https://wearenila.com/privacy-policy/',
        termsOfServiceUrl: 'https://wearenila.com/algemene-voorwaarden/',
        dataRetentionDays: 365
    },
    
    // Email Configuration (voor fallback)
    email: {
        fallbackEmail: '<EMAIL>',
        supportEmail: '<EMAIL>'
    },
    
    // Feature Flags
    features: {
        enableABTesting: false, // Zet op true voor A/B testing
        enableChatWidget: false, // Zet op true voor live chat
        enablePushNotifications: false,
        enableOfflineMode: false
    },
    
    // SEO Configuration
    seo: {
        siteName: 'NILA - Poetry of Light',
        siteUrl: 'https://landing.wearenila.com', // VERVANG MET ECHTE DOMAIN
        defaultTitle: 'NILA - Poetry of Light | Exclusieve Groepsdemonstratie & Samples',
        defaultDescription: 'Ontdek NILA\'s unieke handgemaakte albast verlichting. Boek nu uw plek voor onze exclusieve groepsdemonstratie of vraag samples aan.',
        defaultImage: '/assets/images/nila-hero-og.jpg',
        twitterHandle: '@wearenila' // VERVANG MET ECHTE TWITTER HANDLE
    },
    
    // Contact Information
    contact: {
        address: {
            street: 'Zijlweg 7',
            city: 'Waalwijk',
            postalCode: '5145 NR',
            country: 'Nederland'
        },
        phone: '+31 (0)416 123 456', // VERVANG MET ECHT TELEFOONNUMMER
        email: '<EMAIL>',
        businessHours: {
            monday: '09:00-17:00',
            tuesday: '09:00-17:00',
            wednesday: '09:00-17:00',
            thursday: '09:00-17:00',
            friday: '09:00-17:00',
            saturday: 'Gesloten',
            sunday: 'Gesloten'
        }
    },
    
    // Social Media Links
    social: {
        instagram: 'https://www.instagram.com/nila.poetryoflight/',
        linkedin: 'https://www.linkedin.com/company/wearenila/',
        facebook: '', // Voeg toe indien van toepassing
        twitter: '', // Voeg toe indien van toepassing
        pinterest: '' // Voeg toe indien van toepassing
    },
    
    // Error Handling
    errorHandling: {
        enableErrorReporting: true,
        sentryDsn: '', // Voeg Sentry DSN toe voor error tracking
        logLevel: 'error' // 'debug', 'info', 'warn', 'error'
    }
};

// Development overrides (alleen actief in development mode)
export const DEVELOPMENT_CONFIG = {
    ...PRODUCTION_CONFIG,
    webhook: {
        ...PRODUCTION_CONFIG.webhook,
        url: 'https://webhook.site/test-endpoint', // Test webhook voor development
        apiKey: 'test-api-key'
    },
    analytics: {
        ...PRODUCTION_CONFIG.analytics,
        googleAnalytics: {
            ...PRODUCTION_CONFIG.analytics.googleAnalytics,
            enabled: false // Disable analytics in development
        },
        facebookPixel: {
            ...PRODUCTION_CONFIG.analytics.facebookPixel,
            enabled: false
        }
    },
    errorHandling: {
        ...PRODUCTION_CONFIG.errorHandling,
        logLevel: 'debug'
    }
};

// Export the appropriate config based on environment
export const CONFIG = import.meta.env.MODE === 'production' 
    ? PRODUCTION_CONFIG 
    : DEVELOPMENT_CONFIG;
