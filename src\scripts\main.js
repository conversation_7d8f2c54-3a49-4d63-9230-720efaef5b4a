// NILA Sales Landing Page - Main JavaScript
// Author: NILA Team
// Version: 1.0.0

// Import configuration
import { CONFIG } from '../config/production.js';

// DOM Elements
const elements = {
    sampleModal: document.getElementById('sample-modal'),
    sampleForm: document.getElementById('sample-form'),
    submitButton: document.getElementById('submit-sample-form'),
    submitText: document.getElementById('submit-text'),
    submitLoading: document.getElementById('submit-loading'),
    floatingCta: document.getElementById('floating-cta'),
    stickyCtaBar: document.getElementById('sticky-cta-bar')
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Main initialization function
function initializeApp() {
    console.log('🎨 NILA Landing Page - Initializing...');

    // Force close any open modals
    if (elements.sampleModal) {
        elements.sampleModal.classList.add('hidden');
        document.body.style.overflow = '';
    }

    // Initialize scroll animations
    initializeScrollAnimations();

    // Initialize form validation
    initializeFormValidation();

    // Initialize analytics
    initializeAnalytics();

    // Initialize event listeners
    initializeEventListeners();

    // Initialize floating CTA
    initializeFloatingCTA();

    // Initialize sticky CTA bar
    initializeStickyCtaBar();

    // Initialize hero parallax effect
    initializeHeroParallax();

    // Initialize Calendly integration
    initializeCalendlyIntegration();

    // Initialize Calendly styling monitor
    initializeCalendlyStyleMonitor();

    console.log('✅ NILA Landing Page - Ready!');
}

// Scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Special handling for Nederlands vakmanschap image
    const nederlandsVakmanschapImage = document.querySelector('.nederlands-vakmanschap-image');
    if (nederlandsVakmanschapImage) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    console.log('Nederlands vakmanschap image is in view - applying zoom');
                    entry.target.style.transform = 'scale(1.15)';
                    entry.target.style.objectPosition = '33% 50%';
                } else {
                    entry.target.style.transform = 'scale(1.0)';
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -100px 0px'
        });

        imageObserver.observe(nederlandsVakmanschapImage);
    }
}

// Form validation
function initializeFormValidation() {
    const form = elements.sampleForm;
    if (!form) return;

    // Real-time validation
    const inputs = form.querySelectorAll('input[required], select[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
}

// Validate individual field
function validateField(event) {
    const field = event.target;
    const value = field.value.trim();

    // Remove existing error styling
    field.classList.remove('border-red-500');

    // Validation rules
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'Dit veld is verplicht');
        return false;
    }

    if (field.type === 'email' && value && !isValidEmail(value)) {
        showFieldError(field, 'Voer een geldig e-mailadres in');
        return false;
    }

    if (field.name === 'postalCode' && value && !isValidPostalCode(value)) {
        showFieldError(field, 'Voer een geldige postcode in');
        return false;
    }

    return true;
}

// Clear field error
function clearFieldError(event) {
    const field = event.target;
    field.classList.remove('border-red-500');

    // Remove error message
    const errorMsg = field.parentNode.querySelector('.error-message');
    if (errorMsg) {
        errorMsg.remove();
    }
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('border-red-500');

    // Remove existing error message
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Postal code validation (Dutch format)
function isValidPostalCode(postalCode) {
    const dutchPostalRegex = /^[1-9][0-9]{3}\s?[A-Za-z]{2}$/;
    return dutchPostalRegex.test(postalCode);
}

// Event listeners
function initializeEventListeners() {
    // Sample form submission
    if (elements.sampleForm) {
        elements.sampleForm.addEventListener('submit', handleSampleFormSubmit);
    }

    // Modal close on outside click
    if (elements.sampleModal) {
        elements.sampleModal.addEventListener('click', (e) => {
            if (e.target === elements.sampleModal) {
                closeSampleForm();
            }
        });
    }

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !elements.sampleModal.classList.contains('hidden')) {
            closeSampleForm();
        }
    });
}

// Initialize hero parallax effect
function initializeHeroParallax() {
    const heroImage = document.querySelector('.hero-image');
    if (!heroImage) return;

    let ticking = false;

    function updateParallax() {
        const scrolled = window.pageYOffset;
        const heroSection = document.getElementById('hero');
        const heroHeight = heroSection.offsetHeight;

        // Only apply parallax while hero is visible
        if (scrolled < heroHeight) {
            const parallaxSpeed = 0.5;
            const yPos = scrolled * parallaxSpeed;

            // Apply subtle parallax transform with correct scale
            heroImage.style.transform = `scale(1.4) translateY(${yPos}px)`;

            // Subtle opacity change for depth
            const opacity = Math.max(0.8, 1 - (scrolled / heroHeight) * 0.2);
            heroImage.style.opacity = opacity;
        }

        ticking = false;
    }

    function handleParallaxScroll() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    // Add scroll listener for parallax
    window.addEventListener('scroll', handleParallaxScroll, { passive: true });
}

// Initialize floating CTA
function initializeFloatingCTA() {
    if (!elements.floatingCta) return;

    let isVisible = false;

    function toggleFloatingCTA() {
        const scrollPosition = window.scrollY;
        const heroHeight = window.innerHeight; // Approximately hero section height

        if (scrollPosition > heroHeight && !isVisible) {
            elements.floatingCta.classList.remove('hidden');
            isVisible = true;

            // Track floating CTA appearance
            trackEvent('floating_cta_shown', {
                scroll_position: scrollPosition
            });
        } else if (scrollPosition <= heroHeight && isVisible) {
            elements.floatingCta.classList.add('hidden');
            isVisible = false;
        }
    }

    // Throttle scroll events for performance
    let ticking = false;
    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleFloatingCTA();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', handleScroll);

    // Track floating CTA clicks
    elements.floatingCta.addEventListener('click', () => {
        trackEvent('floating_cta_click', {
            cta_type: 'floating_demo_button'
        });
    });
}

// Initialize sticky CTA bar
function initializeStickyCtaBar() {
    if (!elements.stickyCtaBar) return;

    let isVisible = false;
    let hasBeenShown = false;

    function toggleStickyCtaBar() {
        const scrollPosition = window.scrollY;
        const heroHeight = window.innerHeight * 1.2; // Show after scrolling past hero
        const documentHeight = document.documentElement.scrollHeight;
        const windowHeight = window.innerHeight;
        const footerOffset = 200; // Hide when near footer

        // Don't show if near bottom of page
        const nearBottom = scrollPosition + windowHeight > documentHeight - footerOffset;

        if (scrollPosition > heroHeight && !isVisible && !nearBottom) {
            elements.stickyCtaBar.classList.remove('hidden');
            elements.stickyCtaBar.style.transform = 'translateY(0)';
            isVisible = true;

            // Track first appearance
            if (!hasBeenShown) {
                trackEvent('sticky_cta_shown', {
                    scroll_position: scrollPosition
                });
                hasBeenShown = true;
            }
        } else if ((scrollPosition <= heroHeight || nearBottom) && isVisible) {
            elements.stickyCtaBar.style.transform = 'translateY(100%)';
            setTimeout(() => {
                if (!isVisible) elements.stickyCtaBar.classList.add('hidden');
            }, 300);
            isVisible = false;
        }
    }

    // Throttle scroll events for performance
    let ticking = false;
    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleStickyCtaBar();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', handleScroll);
}

// Hide sticky bar (user action)
window.hideStickyBar = function() {
    if (elements.stickyCtaBar) {
        elements.stickyCtaBar.style.transform = 'translateY(100%)';
        setTimeout(() => {
            elements.stickyCtaBar.classList.add('hidden');
        }, 300);

        trackEvent('sticky_cta_dismissed', {
            user_action: true
        });
    }
};

// Open group demo (Calendly)
window.openGroupDemo = function() {
    console.log('🗓️ Opening group demo booking...');

    // Track event
    trackEvent('cta_click', {
        cta_type: 'group_demo',
        cta_location: 'hero'
    });

    // Open Calendly with custom styling
    if (typeof Calendly !== 'undefined') {
        Calendly.initPopupWidget({
            url: CONFIG.calendly.url,
            utm: {
                utmCampaign: CONFIG.calendly.utm.utmCampaign,
                utmSource: CONFIG.calendly.utm.utmSource,
                utmMedium: CONFIG.calendly.utm.utmMedium + '_group_demo'
            },
            // Custom styling options
            prefill: {
                name: '',
                email: '',
                customAnswers: {
                    a1: 'NILA Landing Page'
                }
            },
            // Custom colors to match NILA brand
            styles: {
                primaryColor: '#DCC8B6', // NILA Natural Beige
                textColor: '#373534',    // NILA Night Black
                backgroundColor: '#F8F4EC' // NILA Alabaster White
            }
        });

        // Apply additional custom styling after popup opens
        setTimeout(() => {
            applyCustomCalendlyStyles();
            injectCalendlyCustomCSS();
        }, 500);
    } else {
        console.error('Calendly not loaded');
        // Fallback: open in new window
        window.open(CONFIG.calendly.url, '_blank');
    }
};

// Open sample form
window.openSampleForm = function() {
    console.log('📦 Opening sample form...');

    // Track event
    trackEvent('cta_click', {
        cta_type: 'sample_request',
        cta_location: 'hero'
    });

    elements.sampleModal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Focus first input
    const firstInput = elements.sampleForm.querySelector('input');
    if (firstInput) {
        setTimeout(() => firstInput.focus(), 100);
    }
};

// Close sample form
window.closeSampleForm = function() {
    console.log('❌ Closing sample form...');

    elements.sampleModal.classList.add('hidden');
    document.body.style.overflow = '';

    // Reset form
    elements.sampleForm.reset();
    clearAllFieldErrors();
};

// Clear all field errors
function clearAllFieldErrors() {
    const errorMessages = elements.sampleForm.querySelectorAll('.error-message');
    errorMessages.forEach(msg => msg.remove());

    const errorFields = elements.sampleForm.querySelectorAll('.border-red-500');
    errorFields.forEach(field => field.classList.remove('border-red-500'));
}

// Handle sample form submission
async function handleSampleFormSubmit(event) {
    event.preventDefault();
    console.log('📝 Submitting sample form...');

    // Validate form
    if (!validateForm()) {
        console.log('❌ Form validation failed');
        return;
    }

    // Show loading state
    setSubmitLoading(true);

    try {
        // Prepare form data
        const formData = new FormData(elements.sampleForm);
        const payload = createWebhookPayload(formData);

        // Submit to webhook
        const response = await submitToWebhook(payload);

        if (response.success) {
            console.log('✅ Sample request submitted successfully');
            showSuccessMessage();
            trackEvent('form_submit', {
                form_type: 'sample_request',
                success: true
            });
        } else {
            throw new Error(response.error || 'Submission failed');
        }

    } catch (error) {
        console.error('❌ Sample form submission error:', error);
        showErrorMessage(error.message);
        trackEvent('form_submit', {
            form_type: 'sample_request',
            success: false,
            error: error.message
        });
    } finally {
        setSubmitLoading(false);
    }
}

// Validate entire form
function validateForm() {
    const requiredFields = elements.sampleForm.querySelectorAll('input[required], select[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });

    // Check GDPR consent
    const gdprConsent = document.getElementById('gdprConsent');
    if (!gdprConsent.checked) {
        showFieldError(gdprConsent, 'U moet akkoord gaan met het privacybeleid');
        isValid = false;
    }

    return isValid;
}

// Create webhook payload
function createWebhookPayload(formData) {
    return {
        timestamp: new Date().toISOString(),
        source: "nila_landing_samples",
        contact: {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            company: formData.get('company') || '',
            email: formData.get('email'),
            phone: formData.get('phone') || '',
            address: {
                street: formData.get('street'),
                city: formData.get('city'),
                postalCode: formData.get('postalCode'),
                country: formData.get('country')
            }
        },
        projectDetails: formData.get('projectDetails') || ''
    };
}

// Submit to webhook with retry logic
async function submitToWebhook(payload, retryCount = 0) {
    const maxRetries = CONFIG.webhook.retryAttempts;

    try {
        const response = await fetch(CONFIG.webhook.url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.webhook.apiKey}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        if (retryCount < maxRetries) {
            console.log(`🔄 Retrying webhook submission (${retryCount + 1}/${maxRetries})...`);
            await new Promise(resolve => setTimeout(resolve, CONFIG.webhook.retryDelay * (retryCount + 1)));
            return submitToWebhook(payload, retryCount + 1);
        }
        throw error;
    }
}

// Set submit button loading state
function setSubmitLoading(isLoading) {
    if (isLoading) {
        elements.submitText.classList.add('hidden');
        elements.submitLoading.classList.remove('hidden');
        elements.submitButton.disabled = true;
    } else {
        elements.submitText.classList.remove('hidden');
        elements.submitLoading.classList.add('hidden');
        elements.submitButton.disabled = false;
    }
}

// Show success message
function showSuccessMessage() {
    // Replace form content with success message
    const formContainer = elements.sampleForm.parentNode;
    formContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-xl font-bold text-nila-navy mb-2">Bedankt voor uw aanvraag!</h3>
            <p class="text-nila-dark-gray mb-6">
                Uw sample aanvraag is succesvol verzonden. U ontvangt binnen 24 uur een bevestiging
                per e-mail en uw samples worden binnen 3-5 werkdagen verzonden.
            </p>
            <button onclick="closeSampleForm()" class="btn-primary">
                Sluiten
            </button>
        </div>
    `;

    // Auto-close after 5 seconds
    setTimeout(() => {
        closeSampleForm();
    }, 5000);
}

// Show error message
function showErrorMessage(errorMessage) {
    // Show error at top of form
    const errorDiv = document.createElement('div');
    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
    errorDiv.innerHTML = `
        <strong>Er is een fout opgetreden:</strong> ${errorMessage}
        <br><small>Probeer het opnieuw of neem contact met ons op.</small>
    `;

    elements.sampleForm.insertBefore(errorDiv, elements.sampleForm.firstChild);

    // Remove error after 10 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 10000);
}

// Analytics initialization
function initializeAnalytics() {
    // Google Analytics 4
    if (CONFIG.analytics.googleAnalytics.enabled && CONFIG.analytics.googleAnalytics.measurementId) {
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', CONFIG.analytics.googleAnalytics.measurementId);
        console.log('📊 Google Analytics initialized');
    }

    // Facebook Pixel
    if (CONFIG.analytics.facebookPixel.enabled && CONFIG.analytics.facebookPixel.pixelId) {
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', CONFIG.analytics.facebookPixel.pixelId);
        fbq('track', 'PageView');
        console.log('📊 Facebook Pixel initialized');
    }

    // Hotjar (optional)
    if (CONFIG.analytics.hotjar.enabled && CONFIG.analytics.hotjar.siteId) {
        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:CONFIG.analytics.hotjar.siteId,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        console.log('📊 Hotjar initialized');
    }
}

// Track events
function trackEvent(eventName, parameters = {}) {
    console.log(`📊 Tracking event: ${eventName}`, parameters);

    // Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, parameters);
    }

    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', eventName, parameters);
    }
}

// Utility: Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Calendly Integration - Dynamic data storage
const CALENDLY_DATA = {
    userUri: null, // Will be fetched dynamically
    eventTypeUri: null, // Will be fetched dynamically
    organizationUri: null // Will be fetched dynamically
};

function initializeCalendlyIntegration() {
    console.log('🗓️ Initializing Calendly integration...');

    // Try to get real Calendly data first
    updateDemonstrationTimes().catch((error) => {
        console.error('❌ Calendly API failed:', error);
        console.log('💡 Since there are no scheduled events yet, you can:');
        console.log('   1. Book a test demo at: https://calendly.com/spanjaardagency/nila-demo');
        console.log('   2. Or use mock data for development: useMockDemoData()');
    });

    // Refresh times every 10 minutes
    setInterval(() => {
        updateDemonstrationTimes().catch((error) => {
            console.error('❌ Calendly API refresh failed:', error);
        });
    }, 10 * 60 * 1000);
}

async function updateDemonstrationTimes() {
    try {
        console.log('🔄 Fetching demonstration times from Calendly...');

        // Get user info first if not cached
        if (!CALENDLY_DATA.userUri) {
            await fetchCalendlyUserInfo();
        }

        // Get next available demonstration time
        const nextDemo = await getNextDemonstrationTime();

        if (nextDemo) {
            // Update alle elementen met demonstratie tijden
            const demoElements = document.querySelectorAll('[data-demo-time]');
            demoElements.forEach(element => {
                const format = element.getAttribute('data-demo-format') || 'full';
                element.textContent = formatDemoTime(nextDemo, format);
            });

            // Update beschikbare plaatsen
            const availabilityElements = document.querySelectorAll('[data-demo-availability]');
            availabilityElements.forEach(element => {
                const spotsText = nextDemo.availableSpots > 0
                    ? `Nog slechts ${nextDemo.availableSpots} plaatsen beschikbaar`
                    : 'Beperkte plaatsen beschikbaar';
                element.textContent = spotsText;
            });

            // Update andere demo-gerelateerde teksten
            updateDemoRelatedTexts(nextDemo);

            console.log('✅ Demonstration times updated from Calendly');
        } else {
            console.log('⚠️ No upcoming demonstrations found in Calendly');
            console.log('💡 Creating simulated demo data for testing...');

            // Create simulated demo data based on realistic business schedule
            const simulatedDemo = createSimulatedDemoData();

            // Update elements with simulated data
            updateDemoElementsWithData(simulatedDemo);

            console.log('✅ Simulated demo data applied for testing purposes');
        }
    } catch (error) {
        console.error('❌ Error updating demonstration times:', error);
        // Don't use fallback - just throw error
        throw error;
    }
}

async function fetchCalendlyUserInfo() {
    try {
        const response = await fetch(`${CONFIG.calendly.baseUrl}/users/me`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        CALENDLY_DATA.userUri = data.resource.uri;
        CALENDLY_DATA.organizationUri = data.resource.current_organization;
        console.log('✅ Calendly user info fetched');
        console.log('👤 User URI:', CALENDLY_DATA.userUri);
        console.log('🏢 Organization URI:', CALENDLY_DATA.organizationUri);

        // Get event types
        await fetchEventTypes();

    } catch (error) {
        console.error('❌ Error fetching Calendly user info:', error);
        throw error;
    }
}

async function fetchEventTypes() {
    try {
        const response = await fetch(`${CONFIG.calendly.baseUrl}/event_types?user=${CALENDLY_DATA.userUri}`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Find demonstration event type (look for "nila-demo" specifically first)
        let demoEventType = data.collection.find(eventType =>
            eventType.scheduling_url && eventType.scheduling_url.includes('nila-demo')
        );

        // Fallback to keywords if specific URL not found
        if (!demoEventType) {
            demoEventType = data.collection.find(eventType =>
                eventType.name.toLowerCase().includes('demo') ||
                eventType.name.toLowerCase().includes('demonstratie') ||
                eventType.name.toLowerCase().includes('groep') ||
                eventType.name.toLowerCase().includes('nila')
            );
        }

        if (demoEventType) {
            CALENDLY_DATA.eventTypeUri = demoEventType.uri;
            console.log('✅ Found demonstration event type:', demoEventType.name, demoEventType.scheduling_url);
        } else {
            // Use first available event type as fallback
            CALENDLY_DATA.eventTypeUri = data.collection[0]?.uri;
            console.log('⚠️ No specific demo event type found, using first available:', data.collection[0]?.name);
        }

    } catch (error) {
        console.error('❌ Error fetching event types:', error);
        throw error;
    }
}

async function getNextDemonstrationTime() {
    try {
        if (!CALENDLY_DATA.eventTypeUri) {
            throw new Error('No event type URI available');
        }

        console.log('🔍 Searching for available demonstration times...');
        console.log('📋 Event Type URI:', CALENDLY_DATA.eventTypeUri);

        // Get available times for the next 30 days
        const startTime = new Date().toISOString();
        const endTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

        console.log('📅 Search range:', { startTime, endTime });

        // Use the correct endpoint for available times
        const url = `${CONFIG.calendly.baseUrl}/event_type_available_times?event_type=${CALENDLY_DATA.eventTypeUri}&start_time=${startTime}&end_time=${endTime}`;
        console.log('🔗 API URL (available times):', url);

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Available times API failed:', response.status, response.statusText, errorText);
            throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('📊 Available times response:', data);
        console.log(`🔍 Found ${data.collection.length} available time slots`);

        // Log available times for debugging
        data.collection.forEach((slot, index) => {
            console.log(`⏰ Slot ${index + 1}:`, {
                start_time: new Date(slot.start_time).toLocaleString('nl-NL'),
                status: slot.status,
                invitees_counter: slot.invitees_counter
            });
        });

        // Find the next available slot
        const nextSlot = data.collection.find(slot => slot.status === 'available');

        if (nextSlot) {
            console.log('🎯 Found next available slot:', nextSlot);

            // Get event type details for max participants
            const eventTypeId = CALENDLY_DATA.eventTypeUri.split('/').pop();
            const eventDetailsResponse = await fetch(`${CONFIG.calendly.baseUrl}/event_types/${eventTypeId}`, {
                headers: {
                    'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            let maxSpots = 12; // Default
            if (eventDetailsResponse.ok) {
                const eventDetails = await eventDetailsResponse.json();
                // Check for max invitees per event
                maxSpots = eventDetails.resource.max_invitees_per_event || 12;
                console.log('📊 Event type details:', {
                    name: eventDetails.resource.name,
                    max_invitees: maxSpots,
                    duration: eventDetails.resource.duration
                });
            }

            // Calculate available spots
            const bookedSpots = nextSlot.invitees_counter?.total || 0;
            const availableSpots = Math.max(0, maxSpots - bookedSpots);

            const result = {
                date: new Date(nextSlot.start_time),
                availableSpots: availableSpots,
                totalSpots: maxSpots,
                bookedSpots: bookedSpots,
                slotStatus: nextSlot.status
            };

            console.log('✅ Next demo time found:', result);
            return result;
        }

        console.log('⚠️ No available demonstration slots found');
        return null;

    } catch (error) {
        console.error('❌ Error fetching available demonstration times:', error);
        return null;
    }
}

function updateDemoRelatedTexts(demo) {
    // Update hero subtitle with dynamic timing
    const heroSubtitle = document.querySelector('p[class*="text-xl md:text-2xl"]');
    if (heroSubtitle && heroSubtitle.textContent.includes('30 minuten')) {
        const timeText = formatDemoTime(demo, 'contextual');
        heroSubtitle.textContent = `Ontdek de mogelijkheden met Nila verlichting tijdens een exclusieve online demonstratie ${timeText}`;
    }

    // Update urgency messages
    const urgencyElements = document.querySelectorAll('[data-demo-urgency]');
    urgencyElements.forEach(element => {
        const urgencyText = demo.availableSpots <= 3
            ? `Laatste ${demo.availableSpots} plaatsen!`
            : 'Beperkte plaatsen beschikbaar';
        element.textContent = urgencyText;
    });

    // Update CTA button texts with dynamic timing
    const ctaButtons = document.querySelectorAll('button[onclick*="openGroupDemo"]');
    ctaButtons.forEach(button => {
        // Update button text if it contains generic demo text
        const buttonText = button.textContent;
        if (buttonText.includes('Reserveer') && !buttonText.includes('sample')) {
            // Keep the main text but add urgency if spots are low
            if (demo.availableSpots <= 3) {
                const badge = button.querySelector('.animate-pulse');
                if (badge) {
                    badge.textContent = `${demo.availableSpots} plaatsen`;
                }
            }
        }
    });
}

function useFallbackDemoTimes() {
    console.log('🔄 Using fallback demo times...');

    // Fallback to mock data when Calendly API fails
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);

    const fallbackDemo = {
        date: tomorrow,
        availableSpots: 4, // Fixed number for consistency
        totalSpots: 12
    };

    console.log('📅 Fallback demo data:', fallbackDemo);

    // Update elements with fallback data
    const demoElements = document.querySelectorAll('[data-demo-time]');
    console.log(`🔍 Found ${demoElements.length} demo time elements`);
    demoElements.forEach((element, index) => {
        const format = element.getAttribute('data-demo-format') || 'full';
        const newText = formatDemoTime(fallbackDemo, format);
        console.log(`📝 Updating element ${index + 1}: "${element.textContent}" → "${newText}" (format: ${format})`);
        element.textContent = newText;
    });

    const availabilityElements = document.querySelectorAll('[data-demo-availability]');
    console.log(`🔍 Found ${availabilityElements.length} availability elements`);
    availabilityElements.forEach((element, index) => {
        const newText = `Nog slechts ${fallbackDemo.availableSpots} plaatsen beschikbaar`;
        console.log(`📝 Updating availability ${index + 1}: "${element.textContent}" → "${newText}"`);
        element.textContent = newText;
    });

    const urgencyElements = document.querySelectorAll('[data-demo-urgency]');
    console.log(`🔍 Found ${urgencyElements.length} urgency elements`);
    urgencyElements.forEach((element, index) => {
        const newText = `Laatste ${fallbackDemo.availableSpots} plaatsen!`;
        console.log(`📝 Updating urgency ${index + 1}: "${element.textContent}" → "${newText}"`);
        element.textContent = newText;
    });

    // Update other demo-related texts with fallback
    updateDemoRelatedTexts(fallbackDemo);

    console.log('✅ Fallback demo times applied');
}

function useRealisticFallbackTimes() {
    console.log('🔄 Using realistic fallback demo times...');

    // Create realistic demo times based on typical business schedule
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentHour = now.getHours();

    let nextDemoDate = new Date(now);

    // Find next realistic demo time (Tuesday or Thursday at 14:00)
    if (currentDay === 0 || currentDay === 1 || (currentDay === 2 && currentHour < 14)) {
        // If Sunday, Monday, or Tuesday before 14:00 -> next Tuesday 14:00
        nextDemoDate.setDate(now.getDate() + (2 - currentDay + 7) % 7);
        nextDemoDate.setHours(14, 0, 0, 0);
    } else if (currentDay === 2 || currentDay === 3 || (currentDay === 4 && currentHour < 14)) {
        // If Tuesday after 14:00, Wednesday, or Thursday before 14:00 -> next Thursday 14:00
        nextDemoDate.setDate(now.getDate() + (4 - currentDay + 7) % 7);
        nextDemoDate.setHours(14, 0, 0, 0);
    } else {
        // If Thursday after 14:00, Friday, or Saturday -> next Tuesday 14:00
        nextDemoDate.setDate(now.getDate() + (2 - currentDay + 7) % 7);
        nextDemoDate.setHours(14, 0, 0, 0);
    }

    // If the calculated date is in the past, add a week
    if (nextDemoDate <= now) {
        nextDemoDate.setDate(nextDemoDate.getDate() + 7);
    }

    const realisticDemo = {
        date: nextDemoDate,
        availableSpots: Math.floor(Math.random() * 4) + 2, // 2-5 spots
        totalSpots: 12
    };

    console.log('📅 Realistic demo data:', {
        date: realisticDemo.date.toLocaleString('nl-NL'),
        availableSpots: realisticDemo.availableSpots,
        totalSpots: realisticDemo.totalSpots
    });

    // Update elements with realistic data
    const demoElements = document.querySelectorAll('[data-demo-time]');
    console.log(`🔍 Found ${demoElements.length} demo time elements`);
    demoElements.forEach((element, index) => {
        const format = element.getAttribute('data-demo-format') || 'full';
        const newText = formatDemoTime(realisticDemo, format);
        console.log(`📝 Updating element ${index + 1}: "${element.textContent}" → "${newText}" (format: ${format})`);
        element.textContent = newText;
    });

    const availabilityElements = document.querySelectorAll('[data-demo-availability]');
    console.log(`🔍 Found ${availabilityElements.length} availability elements`);
    availabilityElements.forEach((element, index) => {
        const newText = realisticDemo.availableSpots <= 3
            ? `Nog slechts ${realisticDemo.availableSpots} plaatsen beschikbaar`
            : 'Beperkte plaatsen beschikbaar';
        console.log(`📝 Updating availability ${index + 1}: "${element.textContent}" → "${newText}"`);
        element.textContent = newText;
    });

    const urgencyElements = document.querySelectorAll('[data-demo-urgency]');
    console.log(`🔍 Found ${urgencyElements.length} urgency elements`);
    urgencyElements.forEach((element, index) => {
        const newText = realisticDemo.availableSpots <= 3
            ? `Laatste ${realisticDemo.availableSpots} plaatsen!`
            : 'Beperkt beschikbaar!';
        console.log(`📝 Updating urgency ${index + 1}: "${element.textContent}" → "${newText}"`);
        element.textContent = newText;
    });

    // Update other demo-related texts
    updateDemoRelatedTexts(realisticDemo);

    console.log('✅ Realistic fallback demo times applied');
}

function formatDemoTime(demo, format) {
    const date = demo.date;
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const isTomorrow = date.toDateString() === new Date(now.getTime() + 24 * 60 * 60 * 1000).toDateString();

    const timeString = date.toLocaleTimeString('nl-NL', {
        hour: '2-digit',
        minute: '2-digit'
    });

    switch (format) {
        case 'short':
            if (isToday) return `Vandaag ${timeString}`;
            if (isTomorrow) return `Morgen ${timeString}`;
            return `${date.toLocaleDateString('nl-NL', { weekday: 'short' })} ${timeString}`;

        case 'week':
            return 'Deze Week';

        case 'contextual':
            if (isToday) return `van 30 minuten vandaag om ${timeString}`;
            if (isTomorrow) return `van 30 minuten morgen om ${timeString}`;
            return `van 30 minuten op ${date.toLocaleDateString('nl-NL', {
                weekday: 'long',
                day: 'numeric',
                month: 'long'
            })} om ${timeString}`;

        case 'full':
        default:
            if (isToday) return `Vandaag ${timeString}`;
            if (isTomorrow) return `Morgen ${timeString}`;
            return `${date.toLocaleDateString('nl-NL', {
                weekday: 'long',
                day: 'numeric',
                month: 'long'
            })} ${timeString}`;
    }
}

// Apply custom styling to Calendly popup
function applyCustomCalendlyStyles() {
    try {
        // Find Calendly popup elements
        const popup = document.querySelector('.calendly-popup');
        const overlay = document.querySelector('.calendly-overlay');
        const iframe = document.querySelector('.calendly-popup iframe');

        if (popup) {
            popup.style.borderRadius = '1rem';
            popup.style.overflow = 'hidden';
            popup.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
            popup.style.border = '2px solid #DCC8B6'; // NILA Natural Beige border
        }

        if (overlay) {
            overlay.style.background = 'rgba(55, 53, 52, 0.8)'; // NILA Night Black with opacity
            overlay.style.backdropFilter = 'blur(8px)';
        }

        if (iframe) {
            iframe.style.borderRadius = '1rem';
        }

        // Add custom close button styling
        const closeButton = document.querySelector('.calendly-close-overlay');
        if (closeButton) {
            closeButton.style.background = '#F8F4EC'; // NILA Alabaster White
            closeButton.style.color = '#373534'; // NILA Night Black
            closeButton.style.borderRadius = '50%';
            closeButton.style.width = '40px';
            closeButton.style.height = '40px';
            closeButton.style.display = 'flex';
            closeButton.style.alignItems = 'center';
            closeButton.style.justifyContent = 'center';
            closeButton.style.fontSize = '18px';
            closeButton.style.fontWeight = 'bold';
            closeButton.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            closeButton.style.transition = 'all 0.3s ease';

            closeButton.addEventListener('mouseenter', () => {
                closeButton.style.background = '#CFB5A7'; // NILA Rose
                closeButton.style.transform = 'scale(1.1)';
            });

            closeButton.addEventListener('mouseleave', () => {
                closeButton.style.background = '#F8F4EC'; // NILA Alabaster White
                closeButton.style.transform = 'scale(1)';
            });
        }

        console.log('✅ Custom Calendly styles applied');
    } catch (error) {
        console.error('❌ Error applying custom Calendly styles:', error);
    }
}

// Inject custom CSS for Calendly widget
function injectCalendlyCustomCSS() {
    // Check if custom CSS is already injected
    if (document.getElementById('nila-calendly-custom-css')) {
        return;
    }

    const customCSS = `
        /* NILA Custom Calendly Styling */
        .calendly-popup-wrapper {
            z-index: 9999 !important;
        }

        .calendly-popup {
            border-radius: 1rem !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
            overflow: hidden !important;
            border: 2px solid #DCC8B6 !important;
            max-width: 90vw !important;
            max-height: 90vh !important;
        }

        .calendly-overlay {
            background: rgba(55, 53, 52, 0.8) !important;
            backdrop-filter: blur(8px) !important;
        }

        .calendly-popup iframe {
            border-radius: 1rem !important;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        .calendly-close-overlay {
            background: #F8F4EC !important;
            color: #373534 !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 18px !important;
            font-weight: bold !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
            border: 2px solid #DCC8B6 !important;
        }

        .calendly-close-overlay:hover {
            background: #CFB5A7 !important;
            transform: scale(1.1) !important;
            border-color: #CFB5A7 !important;
        }

        /* Custom styling for mobile */
        @media (max-width: 768px) {
            .calendly-popup {
                margin: 1rem !important;
                max-width: calc(100vw - 2rem) !important;
                max-height: calc(100vh - 2rem) !important;
            }
        }
    `;

    const styleElement = document.createElement('style');
    styleElement.id = 'nila-calendly-custom-css';
    styleElement.textContent = customCSS;
    document.head.appendChild(styleElement);

    console.log('✅ Custom Calendly CSS injected');
}

// Monitor for Calendly popup and apply styling
function initializeCalendlyStyleMonitor() {
    // Inject CSS immediately
    injectCalendlyCustomCSS();

    // Monitor for Calendly popup creation
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if Calendly popup was added
                    if (node.classList && (
                        node.classList.contains('calendly-popup-wrapper') ||
                        node.classList.contains('calendly-overlay') ||
                        node.querySelector && node.querySelector('.calendly-popup')
                    )) {
                        console.log('🎨 Calendly popup detected, applying custom styles...');
                        setTimeout(() => {
                            applyCustomCalendlyStyles();
                        }, 100);
                    }
                }
            });
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('👀 Calendly style monitor initialized');
}

// Debug function for testing Calendly integration
window.testCalendlyIntegration = async function() {
    console.log('🧪 Testing Calendly integration...');
    try {
        await fetchCalendlyUserInfo();
        console.log('✅ User URI:', CALENDLY_DATA.userUri);
        console.log('✅ Event Type URI:', CALENDLY_DATA.eventTypeUri);

        const nextDemo = await getNextDemonstrationTime();
        console.log('✅ Next demo:', nextDemo);

        return { success: true, data: { userUri: CALENDLY_DATA.userUri, eventTypeUri: CALENDLY_DATA.eventTypeUri, nextDemo } };
    } catch (error) {
        console.error('❌ Calendly integration test failed:', error);
        return { success: false, error: error.message };
    }
};

// Test function to force update demo times from Calendly
async function testDemoUpdate() {
    console.log('🧪 Testing real Calendly demo time updates...');
    try {
        await updateDemonstrationTimes();
        return 'Demo times updated with real Calendly data';
    } catch (error) {
        console.error('❌ Failed to get real Calendly data:', error);
        return 'Failed to get real Calendly data - check API token and permissions';
    }
}

// Test Calendly API connection and permissions
async function testCalendlyAPI() {
    console.log('🧪 Testing Calendly API connection and permissions...');

    try {
        console.log('📡 Testing API token...');
        const response = await fetch(`${CONFIG.calendly.baseUrl}/users/me`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const userData = await response.json();
        console.log('✅ API Token works! User:', userData.resource.name);
        console.log('📧 Email:', userData.resource.email);
        console.log('🔗 User URI:', userData.resource.uri);
        console.log('🏢 Organization:', userData.resource.current_organization);

        // Check organization details for subscription info
        if (userData.resource.current_organization) {
            console.log('🔍 Checking organization details...');
            const orgResponse = await fetch(userData.resource.current_organization, {
                headers: {
                    'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (orgResponse.ok) {
                const orgData = await orgResponse.json();
                console.log('🏢 Organization details:', {
                    name: orgData.resource.name,
                    uri: orgData.resource.uri
                });
            } else {
                console.log('⚠️ Cannot access organization details (may require admin permissions)');
            }
        }

        // Test event types
        console.log('📅 Fetching event types...');
        const eventResponse = await fetch(`${CONFIG.calendly.baseUrl}/event_types?user=${userData.resource.uri}`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!eventResponse.ok) {
            throw new Error(`Event types HTTP ${eventResponse.status}: ${eventResponse.statusText}`);
        }

        const eventData = await eventResponse.json();
        console.log('✅ Found event types:', eventData.collection.length);

        eventData.collection.forEach((event, index) => {
            console.log(`📋 Event ${index + 1}:`, {
                name: event.name,
                url: event.scheduling_url,
                active: event.active
            });
        });

        // Look for nila-demo specifically
        const nilaDemo = eventData.collection.find(event =>
            event.scheduling_url && event.scheduling_url.includes('nila-demo')
        );

        if (nilaDemo) {
            console.log('🎯 Found NILA Demo event:', nilaDemo.name);
            console.log('🔗 URL:', nilaDemo.scheduling_url);

            // Test scheduled events access with both methods
            console.log('🔍 Testing scheduled events access...');
            await testScheduledEventsAccess(userData.resource.uri, userData.resource.current_organization);

            return { success: true, message: 'Calendly API working and NILA Demo found!' };
        } else {
            console.log('⚠️ NILA Demo event not found in event types');
            return { success: false, message: 'NILA Demo event not found' };
        }

    } catch (error) {
        console.error('❌ Calendly API test failed:', error);
        return { success: false, error: error.message };
    }
}

// Test scheduled events access specifically
async function testScheduledEventsAccess(userUri, organizationUri) {
    const startTime = new Date().toISOString();
    const endTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

    // Test user-based query
    console.log('🔍 Testing user-based scheduled events query...');
    try {
        const userResponse = await fetch(
            `${CONFIG.calendly.baseUrl}/scheduled_events?user=${userUri}&min_start_time=${startTime}&max_start_time=${endTime}&status=active`,
            {
                headers: {
                    'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (userResponse.ok) {
            const userData = await userResponse.json();
            console.log(`✅ User-based query successful: ${userData.collection.length} events found`);
        } else {
            const errorText = await userResponse.text();
            console.log(`❌ User-based query failed: ${userResponse.status} - ${errorText}`);
        }
    } catch (error) {
        console.log('❌ User-based query error:', error.message);
    }

    // Test organization-based query
    if (organizationUri) {
        console.log('🔍 Testing organization-based scheduled events query...');
        try {
            const orgResponse = await fetch(
                `${CONFIG.calendly.baseUrl}/scheduled_events?organization=${organizationUri}&min_start_time=${startTime}&max_start_time=${endTime}&status=active`,
                {
                    headers: {
                        'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (orgResponse.ok) {
                const orgData = await orgResponse.json();
                console.log(`✅ Organization-based query successful: ${orgData.collection.length} events found`);
            } else {
                const errorText = await orgResponse.text();
                console.log(`❌ Organization-based query failed: ${orgResponse.status} - ${errorText}`);
            }
        } catch (error) {
            console.log('❌ Organization-based query error:', error.message);
        }
    }
}

// Test function to check scheduled events
async function checkScheduledEvents() {
    console.log('🔍 Checking all scheduled events...');

    try {
        // Ensure we have user info
        if (!CALENDLY_DATA.userUri) {
            await fetchCalendlyUserInfo();
        }

        const startTime = new Date().toISOString();
        const endTime = new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString();

        const response = await fetch(
            `${CONFIG.calendly.baseUrl}/scheduled_events?user=${CALENDLY_DATA.userUri}&min_start_time=${startTime}&max_start_time=${endTime}&status=active&sort=start_time:asc`,
            {
                headers: {
                    'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        console.log(`📊 Total scheduled events: ${data.collection.length}`);
        console.log('🎯 Looking for event type:', CALENDLY_DATA.eventTypeUri);

        data.collection.forEach((event, index) => {
            const isNilaDemo = event.event_type === CALENDLY_DATA.eventTypeUri;
            console.log(`${isNilaDemo ? '🎯' : '📅'} Event ${index + 1}:`, {
                name: event.name,
                start_time: new Date(event.start_time).toLocaleString('nl-NL'),
                event_type: event.event_type,
                status: event.status,
                is_nila_demo: isNilaDemo
            });
        });

        const nilaEvents = data.collection.filter(event => event.event_type === CALENDLY_DATA.eventTypeUri);
        console.log(`🎯 Found ${nilaEvents.length} NILA Demo events`);

        return {
            total: data.collection.length,
            nilaEvents: nilaEvents.length,
            events: data.collection
        };

    } catch (error) {
        console.error('❌ Error checking scheduled events:', error);
        return { error: error.message };
    }
}

// Test all possible scheduled events queries
async function testAllScheduledEventsQueries() {
    console.log('🔍 Testing all possible scheduled events queries...');

    try {
        // Ensure we have user info
        if (!CALENDLY_DATA.userUri) {
            await fetchCalendlyUserInfo();
        }

        const startTime = new Date().toISOString();
        const endTime = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(); // 1 year

        const queries = [
            // Basic queries
            `user=${CALENDLY_DATA.userUri}`,
            `organization=${CALENDLY_DATA.organizationUri}`,

            // With time range
            `user=${CALENDLY_DATA.userUri}&min_start_time=${startTime}&max_start_time=${endTime}`,
            `organization=${CALENDLY_DATA.organizationUri}&min_start_time=${startTime}&max_start_time=${endTime}`,

            // With status
            `user=${CALENDLY_DATA.userUri}&status=active`,
            `user=${CALENDLY_DATA.userUri}&status=canceled`,
            `organization=${CALENDLY_DATA.organizationUri}&status=active`,

            // Without time constraints (last 30 days)
            `user=${CALENDLY_DATA.userUri}&count=100`,
            `organization=${CALENDLY_DATA.organizationUri}&count=100`,

            // All events (no filters)
            `user=${CALENDLY_DATA.userUri}&count=100&sort=start_time:desc`,
        ];

        for (let i = 0; i < queries.length; i++) {
            const query = queries[i];
            console.log(`\n🔍 Query ${i + 1}/${queries.length}: ${query}`);

            try {
                const response = await fetch(`${CONFIG.calendly.baseUrl}/scheduled_events?${query}`, {
                    headers: {
                        'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log(`✅ Success: ${data.collection.length} events found`);

                    if (data.collection.length > 0) {
                        console.log('📅 Sample events:');
                        data.collection.slice(0, 3).forEach((event, index) => {
                            console.log(`  ${index + 1}. ${event.name} - ${new Date(event.start_time).toLocaleString('nl-NL')}`);
                        });

                        // If we found events, stop here
                        return { success: true, query, events: data.collection };
                    }
                } else {
                    const errorText = await response.text();
                    console.log(`❌ Failed: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                console.log(`❌ Error: ${error.message}`);
            }

            // Small delay between requests
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('\n⚠️ No events found with any query method');
        return { success: false, message: 'No events found with any query method' };

    } catch (error) {
        console.error('❌ Error testing queries:', error);
        return { error: error.message };
    }
}

// Create simulated demo data when no real events exist
function createSimulatedDemoData() {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentHour = now.getHours();

    let nextDemoDate = new Date(now);

    // Find next realistic demo time (Tuesday or Thursday at 14:00)
    if (currentDay === 0 || currentDay === 1 || (currentDay === 2 && currentHour < 14)) {
        // If Sunday, Monday, or Tuesday before 14:00 -> next Tuesday 14:00
        nextDemoDate.setDate(now.getDate() + (2 - currentDay + 7) % 7);
        nextDemoDate.setHours(14, 0, 0, 0);
    } else if (currentDay === 2 || currentDay === 3 || (currentDay === 4 && currentHour < 14)) {
        // If Tuesday after 14:00, Wednesday, or Thursday before 14:00 -> next Thursday 14:00
        nextDemoDate.setDate(now.getDate() + (4 - currentDay + 7) % 7);
        nextDemoDate.setHours(14, 0, 0, 0);
    } else {
        // If Thursday after 14:00, Friday, or Saturday -> next Tuesday 14:00
        nextDemoDate.setDate(now.getDate() + (2 - currentDay + 7) % 7);
        nextDemoDate.setHours(14, 0, 0, 0);
    }

    // If the calculated date is in the past, add a week
    if (nextDemoDate <= now) {
        nextDemoDate.setDate(nextDemoDate.getDate() + 7);
    }

    return {
        date: nextDemoDate,
        availableSpots: 3, // Realistic number for urgency
        totalSpots: 12,
        isSimulated: true
    };
}

// Update demo elements with provided data
function updateDemoElementsWithData(demo) {
    console.log('📅 Updating demo elements with data:', {
        date: demo.date.toLocaleString('nl-NL'),
        availableSpots: demo.availableSpots,
        isSimulated: demo.isSimulated || false
    });

    // Update alle elementen met demonstratie tijden
    const demoElements = document.querySelectorAll('[data-demo-time]');
    demoElements.forEach((element, index) => {
        const format = element.getAttribute('data-demo-format') || 'full';
        const newText = formatDemoTime(demo, format);
        console.log(`📝 Updating demo time ${index + 1}: "${element.textContent}" → "${newText}"`);
        element.textContent = newText;
    });

    // Update beschikbare plaatsen
    const availabilityElements = document.querySelectorAll('[data-demo-availability]');
    availabilityElements.forEach((element, index) => {
        const newText = demo.availableSpots <= 3
            ? `Nog slechts ${demo.availableSpots} plaatsen beschikbaar`
            : 'Beperkte plaatsen beschikbaar';
        console.log(`📝 Updating availability ${index + 1}: "${element.textContent}" → "${newText}"`);
        element.textContent = newText;
    });

    // Update urgency elements
    const urgencyElements = document.querySelectorAll('[data-demo-urgency]');
    urgencyElements.forEach((element, index) => {
        const newText = demo.availableSpots <= 3
            ? `Laatste ${demo.availableSpots} plaatsen!`
            : 'Beperkt beschikbaar!';
        console.log(`📝 Updating urgency ${index + 1}: "${element.textContent}" → "${newText}"`);
        element.textContent = newText;
    });

    // Update other demo-related texts
    updateDemoRelatedTexts(demo);
}

// Test available times specifically
async function testAvailableTimes() {
    console.log('🧪 Testing available times API...');

    try {
        // Ensure we have event type info
        if (!CALENDLY_DATA.eventTypeUri) {
            await fetchCalendlyUserInfo();
        }

        const startTime = new Date().toISOString();
        const endTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

        const url = `${CONFIG.calendly.baseUrl}/event_type_available_times?event_type=${CALENDLY_DATA.eventTypeUri}&start_time=${startTime}&end_time=${endTime}`;
        console.log('🔗 Testing URL:', url);

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ Available times API works!');
            console.log(`📊 Found ${data.collection.length} time slots`);

            data.collection.slice(0, 5).forEach((slot, index) => {
                console.log(`⏰ Slot ${index + 1}:`, {
                    time: new Date(slot.start_time).toLocaleString('nl-NL'),
                    status: slot.status,
                    invitees: slot.invitees_counter?.total || 0
                });
            });

            return { success: true, slots: data.collection.length };
        } else {
            const errorText = await response.text();
            console.error('❌ Available times API failed:', response.status, errorText);
            return { success: false, error: `${response.status}: ${errorText}` };
        }

    } catch (error) {
        console.error('❌ Error testing available times:', error);
        return { success: false, error: error.message };
    }
}

// Make test functions globally available
window.testDemoUpdate = testDemoUpdate;
window.testCalendlyAPI = testCalendlyAPI;
window.checkScheduledEvents = checkScheduledEvents;
window.testAllScheduledEventsQueries = testAllScheduledEventsQueries;
window.testAvailableTimes = testAvailableTimes;

// Export functions for global access
window.NILA = {
    openGroupDemo,
    openSampleForm,
    closeSampleForm,
    trackEvent,
    scrollToElement,
    updateDemonstrationTimes,
    testCalendlyIntegration: window.testCalendlyIntegration,
    testDemoUpdate: window.testDemoUpdate,
    testCalendlyAPI: window.testCalendlyAPI,
    testAllScheduledEventsQueries: window.testAllScheduledEventsQueries,
    testAvailableTimes: window.testAvailableTimes
};
