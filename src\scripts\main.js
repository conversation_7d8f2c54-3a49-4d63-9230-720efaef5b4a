// NILA Sales Landing Page - Main JavaScript
// Author: NILA Team
// Version: 1.0.0

// Import configuration
import { CONFIG } from '../config/production.js';

// DOM Elements
const elements = {
    sampleModal: document.getElementById('sample-modal'),
    sampleForm: document.getElementById('sample-form'),
    submitButton: document.getElementById('submit-sample-form'),
    submitText: document.getElementById('submit-text'),
    submitLoading: document.getElementById('submit-loading'),
    floatingCta: document.getElementById('floating-cta'),
    stickyCtaBar: document.getElementById('sticky-cta-bar')
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Main initialization function
function initializeApp() {
    console.log('🎨 NILA Landing Page - Initializing...');

    // Force close any open modals
    if (elements.sampleModal) {
        elements.sampleModal.classList.add('hidden');
        document.body.style.overflow = '';
    }

    // Initialize scroll animations
    initializeScrollAnimations();

    // Initialize form validation
    initializeFormValidation();

    // Initialize analytics
    initializeAnalytics();

    // Initialize event listeners
    initializeEventListeners();

    // Initialize floating CTA
    initializeFloatingCTA();

    // Initialize sticky CTA bar
    initializeStickyCtaBar();

    // Initialize hero parallax effect
    initializeHeroParallax();

    // Initialize Calendly integration
    initializeCalendlyIntegration();

    console.log('✅ NILA Landing Page - Ready!');
}

// Scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Special handling for Nederlands vakmanschap image
    const nederlandsVakmanschapImage = document.querySelector('.nederlands-vakmanschap-image');
    if (nederlandsVakmanschapImage) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    console.log('Nederlands vakmanschap image is in view - applying zoom');
                    entry.target.style.transform = 'scale(1.15)';
                    entry.target.style.objectPosition = '33% 50%';
                } else {
                    entry.target.style.transform = 'scale(1.0)';
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -100px 0px'
        });

        imageObserver.observe(nederlandsVakmanschapImage);
    }
}

// Form validation
function initializeFormValidation() {
    const form = elements.sampleForm;
    if (!form) return;

    // Real-time validation
    const inputs = form.querySelectorAll('input[required], select[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
}

// Validate individual field
function validateField(event) {
    const field = event.target;
    const value = field.value.trim();

    // Remove existing error styling
    field.classList.remove('border-red-500');

    // Validation rules
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'Dit veld is verplicht');
        return false;
    }

    if (field.type === 'email' && value && !isValidEmail(value)) {
        showFieldError(field, 'Voer een geldig e-mailadres in');
        return false;
    }

    if (field.name === 'postalCode' && value && !isValidPostalCode(value)) {
        showFieldError(field, 'Voer een geldige postcode in');
        return false;
    }

    return true;
}

// Clear field error
function clearFieldError(event) {
    const field = event.target;
    field.classList.remove('border-red-500');

    // Remove error message
    const errorMsg = field.parentNode.querySelector('.error-message');
    if (errorMsg) {
        errorMsg.remove();
    }
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('border-red-500');

    // Remove existing error message
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Postal code validation (Dutch format)
function isValidPostalCode(postalCode) {
    const dutchPostalRegex = /^[1-9][0-9]{3}\s?[A-Za-z]{2}$/;
    return dutchPostalRegex.test(postalCode);
}

// Event listeners
function initializeEventListeners() {
    // Sample form submission
    if (elements.sampleForm) {
        elements.sampleForm.addEventListener('submit', handleSampleFormSubmit);
    }

    // Modal close on outside click
    if (elements.sampleModal) {
        elements.sampleModal.addEventListener('click', (e) => {
            if (e.target === elements.sampleModal) {
                closeSampleForm();
            }
        });
    }

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !elements.sampleModal.classList.contains('hidden')) {
            closeSampleForm();
        }
    });
}

// Initialize hero parallax effect
function initializeHeroParallax() {
    const heroImage = document.querySelector('.hero-image');
    if (!heroImage) return;

    let ticking = false;

    function updateParallax() {
        const scrolled = window.pageYOffset;
        const heroSection = document.getElementById('hero');
        const heroHeight = heroSection.offsetHeight;

        // Only apply parallax while hero is visible
        if (scrolled < heroHeight) {
            const parallaxSpeed = 0.5;
            const yPos = scrolled * parallaxSpeed;

            // Apply subtle parallax transform with correct scale
            heroImage.style.transform = `scale(1.4) translateY(${yPos}px)`;

            // Subtle opacity change for depth
            const opacity = Math.max(0.8, 1 - (scrolled / heroHeight) * 0.2);
            heroImage.style.opacity = opacity;
        }

        ticking = false;
    }

    function handleParallaxScroll() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    // Add scroll listener for parallax
    window.addEventListener('scroll', handleParallaxScroll, { passive: true });
}

// Initialize floating CTA
function initializeFloatingCTA() {
    if (!elements.floatingCta) return;

    let isVisible = false;

    function toggleFloatingCTA() {
        const scrollPosition = window.scrollY;
        const heroHeight = window.innerHeight; // Approximately hero section height

        if (scrollPosition > heroHeight && !isVisible) {
            elements.floatingCta.classList.remove('hidden');
            isVisible = true;

            // Track floating CTA appearance
            trackEvent('floating_cta_shown', {
                scroll_position: scrollPosition
            });
        } else if (scrollPosition <= heroHeight && isVisible) {
            elements.floatingCta.classList.add('hidden');
            isVisible = false;
        }
    }

    // Throttle scroll events for performance
    let ticking = false;
    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleFloatingCTA();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', handleScroll);

    // Track floating CTA clicks
    elements.floatingCta.addEventListener('click', () => {
        trackEvent('floating_cta_click', {
            cta_type: 'floating_demo_button'
        });
    });
}

// Initialize sticky CTA bar
function initializeStickyCtaBar() {
    if (!elements.stickyCtaBar) return;

    let isVisible = false;
    let hasBeenShown = false;

    function toggleStickyCtaBar() {
        const scrollPosition = window.scrollY;
        const heroHeight = window.innerHeight * 1.2; // Show after scrolling past hero
        const documentHeight = document.documentElement.scrollHeight;
        const windowHeight = window.innerHeight;
        const footerOffset = 200; // Hide when near footer

        // Don't show if near bottom of page
        const nearBottom = scrollPosition + windowHeight > documentHeight - footerOffset;

        if (scrollPosition > heroHeight && !isVisible && !nearBottom) {
            elements.stickyCtaBar.classList.remove('hidden');
            elements.stickyCtaBar.style.transform = 'translateY(0)';
            isVisible = true;

            // Track first appearance
            if (!hasBeenShown) {
                trackEvent('sticky_cta_shown', {
                    scroll_position: scrollPosition
                });
                hasBeenShown = true;
            }
        } else if ((scrollPosition <= heroHeight || nearBottom) && isVisible) {
            elements.stickyCtaBar.style.transform = 'translateY(100%)';
            setTimeout(() => {
                if (!isVisible) elements.stickyCtaBar.classList.add('hidden');
            }, 300);
            isVisible = false;
        }
    }

    // Throttle scroll events for performance
    let ticking = false;
    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleStickyCtaBar();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', handleScroll);
}

// Hide sticky bar (user action)
window.hideStickyBar = function() {
    if (elements.stickyCtaBar) {
        elements.stickyCtaBar.style.transform = 'translateY(100%)';
        setTimeout(() => {
            elements.stickyCtaBar.classList.add('hidden');
        }, 300);

        trackEvent('sticky_cta_dismissed', {
            user_action: true
        });
    }
};

// Open group demo (Calendly)
window.openGroupDemo = function() {
    console.log('🗓️ Opening group demo booking...');

    // Track event
    trackEvent('cta_click', {
        cta_type: 'group_demo',
        cta_location: 'hero'
    });

    // Open Calendly
    if (typeof Calendly !== 'undefined') {
        Calendly.initPopupWidget({
            url: CONFIG.calendly.url,
            utm: {
                utmCampaign: CONFIG.calendly.utm.utmCampaign,
                utmSource: CONFIG.calendly.utm.utmSource,
                utmMedium: CONFIG.calendly.utm.utmMedium + '_group_demo'
            }
        });
    } else {
        console.error('Calendly not loaded');
        // Fallback: open in new window
        window.open(CONFIG.calendly.url, '_blank');
    }
};

// Open sample form
window.openSampleForm = function() {
    console.log('📦 Opening sample form...');

    // Track event
    trackEvent('cta_click', {
        cta_type: 'sample_request',
        cta_location: 'hero'
    });

    elements.sampleModal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Focus first input
    const firstInput = elements.sampleForm.querySelector('input');
    if (firstInput) {
        setTimeout(() => firstInput.focus(), 100);
    }
};

// Close sample form
window.closeSampleForm = function() {
    console.log('❌ Closing sample form...');

    elements.sampleModal.classList.add('hidden');
    document.body.style.overflow = '';

    // Reset form
    elements.sampleForm.reset();
    clearAllFieldErrors();
};

// Clear all field errors
function clearAllFieldErrors() {
    const errorMessages = elements.sampleForm.querySelectorAll('.error-message');
    errorMessages.forEach(msg => msg.remove());

    const errorFields = elements.sampleForm.querySelectorAll('.border-red-500');
    errorFields.forEach(field => field.classList.remove('border-red-500'));
}

// Handle sample form submission
async function handleSampleFormSubmit(event) {
    event.preventDefault();
    console.log('📝 Submitting sample form...');

    // Validate form
    if (!validateForm()) {
        console.log('❌ Form validation failed');
        return;
    }

    // Show loading state
    setSubmitLoading(true);

    try {
        // Prepare form data
        const formData = new FormData(elements.sampleForm);
        const payload = createWebhookPayload(formData);

        // Submit to webhook
        const response = await submitToWebhook(payload);

        if (response.success) {
            console.log('✅ Sample request submitted successfully');
            showSuccessMessage();
            trackEvent('form_submit', {
                form_type: 'sample_request',
                success: true
            });
        } else {
            throw new Error(response.error || 'Submission failed');
        }

    } catch (error) {
        console.error('❌ Sample form submission error:', error);
        showErrorMessage(error.message);
        trackEvent('form_submit', {
            form_type: 'sample_request',
            success: false,
            error: error.message
        });
    } finally {
        setSubmitLoading(false);
    }
}

// Validate entire form
function validateForm() {
    const requiredFields = elements.sampleForm.querySelectorAll('input[required], select[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });

    // Check GDPR consent
    const gdprConsent = document.getElementById('gdprConsent');
    if (!gdprConsent.checked) {
        showFieldError(gdprConsent, 'U moet akkoord gaan met het privacybeleid');
        isValid = false;
    }

    return isValid;
}

// Create webhook payload
function createWebhookPayload(formData) {
    return {
        timestamp: new Date().toISOString(),
        source: "nila_landing_samples",
        contact: {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            company: formData.get('company') || '',
            email: formData.get('email'),
            phone: formData.get('phone') || '',
            address: {
                street: formData.get('street'),
                city: formData.get('city'),
                postalCode: formData.get('postalCode'),
                country: formData.get('country')
            }
        },
        projectDetails: formData.get('projectDetails') || ''
    };
}

// Submit to webhook with retry logic
async function submitToWebhook(payload, retryCount = 0) {
    const maxRetries = CONFIG.webhook.retryAttempts;

    try {
        const response = await fetch(CONFIG.webhook.url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.webhook.apiKey}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        if (retryCount < maxRetries) {
            console.log(`🔄 Retrying webhook submission (${retryCount + 1}/${maxRetries})...`);
            await new Promise(resolve => setTimeout(resolve, CONFIG.webhook.retryDelay * (retryCount + 1)));
            return submitToWebhook(payload, retryCount + 1);
        }
        throw error;
    }
}

// Set submit button loading state
function setSubmitLoading(isLoading) {
    if (isLoading) {
        elements.submitText.classList.add('hidden');
        elements.submitLoading.classList.remove('hidden');
        elements.submitButton.disabled = true;
    } else {
        elements.submitText.classList.remove('hidden');
        elements.submitLoading.classList.add('hidden');
        elements.submitButton.disabled = false;
    }
}

// Show success message
function showSuccessMessage() {
    // Replace form content with success message
    const formContainer = elements.sampleForm.parentNode;
    formContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-xl font-bold text-nila-navy mb-2">Bedankt voor uw aanvraag!</h3>
            <p class="text-nila-dark-gray mb-6">
                Uw sample aanvraag is succesvol verzonden. U ontvangt binnen 24 uur een bevestiging
                per e-mail en uw samples worden binnen 3-5 werkdagen verzonden.
            </p>
            <button onclick="closeSampleForm()" class="btn-primary">
                Sluiten
            </button>
        </div>
    `;

    // Auto-close after 5 seconds
    setTimeout(() => {
        closeSampleForm();
    }, 5000);
}

// Show error message
function showErrorMessage(errorMessage) {
    // Show error at top of form
    const errorDiv = document.createElement('div');
    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
    errorDiv.innerHTML = `
        <strong>Er is een fout opgetreden:</strong> ${errorMessage}
        <br><small>Probeer het opnieuw of neem contact met ons op.</small>
    `;

    elements.sampleForm.insertBefore(errorDiv, elements.sampleForm.firstChild);

    // Remove error after 10 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 10000);
}

// Analytics initialization
function initializeAnalytics() {
    // Google Analytics 4
    if (CONFIG.analytics.googleAnalytics.enabled && CONFIG.analytics.googleAnalytics.measurementId) {
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', CONFIG.analytics.googleAnalytics.measurementId);
        console.log('📊 Google Analytics initialized');
    }

    // Facebook Pixel
    if (CONFIG.analytics.facebookPixel.enabled && CONFIG.analytics.facebookPixel.pixelId) {
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', CONFIG.analytics.facebookPixel.pixelId);
        fbq('track', 'PageView');
        console.log('📊 Facebook Pixel initialized');
    }

    // Hotjar (optional)
    if (CONFIG.analytics.hotjar.enabled && CONFIG.analytics.hotjar.siteId) {
        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:CONFIG.analytics.hotjar.siteId,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        console.log('📊 Hotjar initialized');
    }
}

// Track events
function trackEvent(eventName, parameters = {}) {
    console.log(`📊 Tracking event: ${eventName}`, parameters);

    // Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, parameters);
    }

    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', eventName, parameters);
    }
}

// Utility: Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Calendly Integration - Dynamic data storage
const CALENDLY_DATA = {
    userUri: null, // Will be fetched dynamically
    eventTypeUri: null // Will be fetched dynamically
};

function initializeCalendlyIntegration() {
    console.log('🗓️ Initializing Calendly integration...');

    // Update demonstration times from Calendly
    updateDemonstrationTimes();

    // Refresh times every 10 minutes
    setInterval(updateDemonstrationTimes, 10 * 60 * 1000);
}

async function updateDemonstrationTimes() {
    try {
        console.log('🔄 Fetching demonstration times from Calendly...');

        // Get user info first if not cached
        if (!CALENDLY_DATA.userUri) {
            await fetchCalendlyUserInfo();
        }

        // Get next available demonstration time
        const nextDemo = await getNextDemonstrationTime();

        if (nextDemo) {
            // Update alle elementen met demonstratie tijden
            const demoElements = document.querySelectorAll('[data-demo-time]');
            demoElements.forEach(element => {
                const format = element.getAttribute('data-demo-format') || 'full';
                element.textContent = formatDemoTime(nextDemo, format);
            });

            // Update beschikbare plaatsen
            const availabilityElements = document.querySelectorAll('[data-demo-availability]');
            availabilityElements.forEach(element => {
                const spotsText = nextDemo.availableSpots > 0
                    ? `Nog slechts ${nextDemo.availableSpots} plaatsen beschikbaar`
                    : 'Beperkte plaatsen beschikbaar';
                element.textContent = spotsText;
            });

            console.log('✅ Demonstration times updated from Calendly');
        } else {
            console.log('⚠️ No upcoming demonstrations found, using fallback');
            useFallbackDemoTimes();
        }
    } catch (error) {
        console.error('❌ Error updating demonstration times:', error);
        console.log('🔄 Using fallback demonstration times');
        useFallbackDemoTimes();
    }
}

async function fetchCalendlyUserInfo() {
    try {
        const response = await fetch(`${CONFIG.calendly.baseUrl}/users/me`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        CALENDLY_DATA.userUri = data.resource.uri;
        console.log('✅ Calendly user info fetched');

        // Get event types
        await fetchEventTypes();

    } catch (error) {
        console.error('❌ Error fetching Calendly user info:', error);
        throw error;
    }
}

async function fetchEventTypes() {
    try {
        const response = await fetch(`${CONFIG.calendly.baseUrl}/event_types?user=${CALENDLY_DATA.userUri}`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Find demonstration event type (look for keywords like "demo", "demonstratie", etc.)
        const demoEventType = data.collection.find(eventType =>
            eventType.name.toLowerCase().includes('demo') ||
            eventType.name.toLowerCase().includes('demonstratie') ||
            eventType.name.toLowerCase().includes('groep')
        );

        if (demoEventType) {
            CALENDLY_DATA.eventTypeUri = demoEventType.uri;
            console.log('✅ Found demonstration event type:', demoEventType.name);
        } else {
            // Use first available event type as fallback
            CALENDLY_DATA.eventTypeUri = data.collection[0]?.uri;
            console.log('⚠️ No specific demo event type found, using first available');
        }

    } catch (error) {
        console.error('❌ Error fetching event types:', error);
        throw error;
    }
}

async function getNextDemonstrationTime() {
    try {
        if (!CALENDLY_DATA.eventTypeUri) {
            throw new Error('No event type URI available');
        }

        // Get scheduled events for the next 30 days
        const startTime = new Date().toISOString();
        const endTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

        const response = await fetch(
            `${CONFIG.calendly.baseUrl}/scheduled_events?user=${CALENDLY_DATA.userUri}&min_start_time=${startTime}&max_start_time=${endTime}&status=active&sort=start_time:asc`,
            {
                headers: {
                    'Authorization': `Bearer ${CONFIG.calendly.apiToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Find next available demonstration
        const nextEvent = data.collection.find(event =>
            event.event_type === CALENDLY_DATA.eventTypeUri
        );

        if (nextEvent) {
            // Calculate available spots (assuming max 12 participants)
            const maxSpots = 12;
            const bookedSpots = nextEvent.event_memberships?.length || 0;
            const availableSpots = Math.max(0, maxSpots - bookedSpots);

            return {
                date: new Date(nextEvent.start_time),
                availableSpots: availableSpots,
                totalSpots: maxSpots,
                eventUri: nextEvent.uri
            };
        }

        return null;

    } catch (error) {
        console.error('❌ Error fetching next demonstration time:', error);
        return null;
    }
}

function useFallbackDemoTimes() {
    // Fallback to mock data when Calendly API fails
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);

    const fallbackDemo = {
        date: tomorrow,
        availableSpots: Math.floor(Math.random() * 5) + 1,
        totalSpots: 12
    };

    // Update elements with fallback data
    const demoElements = document.querySelectorAll('[data-demo-time]');
    demoElements.forEach(element => {
        const format = element.getAttribute('data-demo-format') || 'full';
        element.textContent = formatDemoTime(fallbackDemo, format);
    });

    const availabilityElements = document.querySelectorAll('[data-demo-availability]');
    availabilityElements.forEach(element => {
        element.textContent = `Nog slechts ${fallbackDemo.availableSpots} plaatsen beschikbaar`;
    });
}

function formatDemoTime(demo, format) {
    const date = demo.date;
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const isTomorrow = date.toDateString() === new Date(now.getTime() + 24 * 60 * 60 * 1000).toDateString();

    const timeString = date.toLocaleTimeString('nl-NL', {
        hour: '2-digit',
        minute: '2-digit'
    });

    switch (format) {
        case 'short':
            if (isToday) return `Vandaag ${timeString}`;
            if (isTomorrow) return `Morgen ${timeString}`;
            return `${date.toLocaleDateString('nl-NL', { weekday: 'short' })} ${timeString}`;

        case 'week':
            return 'Deze Week';

        case 'full':
        default:
            if (isToday) return `Vandaag ${timeString}`;
            if (isTomorrow) return `Morgen ${timeString}`;
            return `${date.toLocaleDateString('nl-NL', {
                weekday: 'long',
                day: 'numeric',
                month: 'long'
            })} ${timeString}`;
    }
}

// Export functions for global access
window.NILA = {
    openGroupDemo,
    openSampleForm,
    closeSampleForm,
    trackEvent,
    scrollToElement,
    updateDemonstrationTimes
};
